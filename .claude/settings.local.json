{"permissions": {"allow": ["<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker-compose ps)", "<PERSON><PERSON>(docker compose:*)", "Bash(docker-compose build:*)", "<PERSON><PERSON>(docker cp:*)", "Ba<PERSON>(docker logs mem0-api --tail 10)", "mcp__sequential-thinking__sequentialthinking", "mcp__chrome-mcp__chrome_navigate", "mcp__chrome-mcp__chrome_get_web_content", "mcp__chrome-mcp__chrome_click_element", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(docker restart:*)", "Bash(docker logs:*)", "Bash(ls:*)", "Bash(docker system prune:*)", "mcp__firecrawl-mcp__firecrawl_scrape", "mcp__firecrawl-mcp__firecrawl_search", "<PERSON><PERSON>(docker-compose:*)", "Bash(pip cache:*)", "Bash(pip install:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我对海鲜过敏，不能吃虾蟹\"\"}],\n    \"\"user_id\"\": \"\"test_user\"\"\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"query\"\": \"\"饮食偏好\"\",\n    \"\"user_id\"\": \"\"test_user\"\",\n    \"\"keyword_search\"\": true\n  }')", "mcp__shrimp-task-manager__list_tasks", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(chmod:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(touch:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": \"\"I love playing tennis on weekends\"\"\n    }\n  ],\n  \"\"user_id\"\": \"\"test_user\"\"\n}')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is <PERSON> and I love pizza\"\"}],\n    \"\"user_id\"\": \"\"test_user_timestamp\"\",\n    \"\"timestamp\"\": 1672531200,\n    \"\"infer\"\": false\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I visited Paris in March 2024\"\"}],\n    \"\"user_id\"\": \"\"test_user_timestamp\"\",\n    \"\"timestamp\"\": 1709251200\n  }')", "<PERSON><PERSON>(cat:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d @/tmp/multimodal_doc_test.json)", "Bash(node:*)", "Bash(pip3 install Pillow)", "Bash(./run-standalone.sh run -d)", "<PERSON><PERSON>(timeout:*)", "Bash(cp:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(docker inspect:*)", "Bash(docker volume inspect:*)", "Bash(docker volume:*)", "Bash(docker container logs:*)", "Bash(ss:*)", "Bash(kill:*)", "<PERSON><PERSON>(sudo mkdir:*)", "Bash(sudo chown -R 1000:1000 /var/lib/mem0/data)", "Bash(sudo chmod -R 755 /var/lib/mem0/data)", "Bash(sudo chown -R 1000:1000 /var/lib/mem0)", "Bash(sudo chmod -R 755 /var/lib/mem0)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": {\n          \"\"type\"\": \"\"pdf_url\"\",\n          \"\"pdf_url\"\": {\n            \"\"url\"\": \"\"https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf\"\"\n          }\n        }\n      }\n    ],\n    \"\"user_id\"\": \"\"test_pdf_user2\"\",\n    \"\"metadata\"\": {\"\"test_type\"\": \"\"pdf_document\"\", \"\"source\"\": \"\"adobe_sample\"\"}\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": {\n          \"\"type\"\": \"\"mdx_url\"\",\n          \"\"mdx_url\"\": {\n            \"\"url\"\": \"\"data:text/plain;base64,6L+Z5piv5LiA5Liq5oqA5pyv5paH5qGj55qE5YaF5a6544CC5paH5qGj5Lit5YyF5ZCr5Lul5LiL6YeN6KaB5L+h5oGv77yaCjEuIOS6uuW3peaZuuiDveeahOWfuuacrOamguW/teWSjOWPkeWxleWOhueoiwoyLiDmnLrlmajlrabkuaDnrpfms5XnmoTliIbnsbvvvJrnm5HnnaPlrabkuaDjgIHml6Dnm5HnnaPlrabkuaDjgIHlvLrljJblrabkuaAKMy4g5rex5bqm5a2m5Lmg5Zyo5Zu+5YOP6K+G5Yir44CB6Ieq54S26K+t6KiA5aSE55CG5Lit55qE5bqU55SoCjQuIOWkp+Wei+ivreiogOaooeWei+eahOW3peS9nOWOn+eQhuWSjOS8mOWMluaWueazlQo1LiBBSeS8pueQhuWSjOWuieWFqOaAp+iAg+iZkQoK6L+Z5Lqb5YaF5a655a+55LqO55CG6Kej546w5LujQUnmioDmnK/pnZ7luLjph43opoHjgIIK\"\"\n          }\n        }\n      }\n    ],\n    \"\"user_id\"\": \"\"test_mdx_user\"\",\n    \"\"metadata\"\": {\"\"test_type\"\": \"\"mdx_document\"\", \"\"source\"\": \"\"base64_text\"\"}\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n  \"\"query\"\": \"\"人工智能和机器学习的技术概念\"\",\n  \"\"user_id\"\": \"\"multimodal_test_user\"\",\n  \"\"limit\"\": 10\n}')", "Bash(-H \"Content-Type: application/json\")", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": [\n        {\n          \"\"type\"\": \"\"text\"\",\n          \"\"text\"\": \"\"我正在学习Mem0记忆系统的架构和核心概念。Mem0是一个智能记忆管理平台，具有以下特点：1) 支持多种LLM提供商如OpenAI、Anthropic等；2) 具备向量存储和语义搜索功能；3) 支持用户、代理和会话级别的记忆作用域；4) 提供RESTful API接口；5) 支持多模态内容处理包括文本、图像和文档。\"\"\n        }\n      ]\n    }\n  ],\n  \"\"user_id\"\": \"\"multimodal_test_user\"\"\n}')", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": \"\"今天我了解到Mem0是一个强大的记忆管理系统，支持多模态内容处理。\"\"\n    }\n  ],\n  \"\"user_id\"\": \"\"multimodal_test_user\"\"\n}')", "Bash(-d '{\n  \"\"query\"\": \"\"平静放松的感受\"\",\n  \"\"user_id\"\": \"\"multimodal_test_user\"\",\n  \"\"limit\"\": 3\n}')", "Bash(-d '{\n  \"\"query\"\": \"\"Python机器学习\"\",\n  \"\"user_id\"\": \"\"test_memory_id_user\"\",\n  \"\"limit\"\": 3\n}')", "<PERSON><PERSON>(echo:*)", "Bash(echo -e \"\\n\\n测试3: 存在的UUID\")", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": \"\"我今天参加了一个重要的商务会议，讨论了新项目的合作方案。\"\"\n    }\n  ],\n  \"\"user_id\"\": \"\"category_test_user\"\",\n  \"\"custom_categories\"\": [\n    {\"\"business\"\": \"\"商务相关活动和决策\"\"},\n    {\"\"meetings\"\": \"\"会议和讨论记录\"\"}\n  ]\n}')", "Bash(-d '{\n  \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我报名了一个Python编程课程，想要深入学习人工智能技术\"\"}],\n  \"\"user_id\"\": \"\"test_user_categories\"\",\n  \"\"custom_categories\"\": [\n    {\"\"学习\"\": \"\"学习计划、教育相关\"\"},\n    {\"\"编程\"\": \"\"编程语言、代码开发\"\"}\n  ]\n}')", "Bash(-d '{\n  \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我在网上购物平台买了一本关于深度学习的书籍\"\"}],\n  \"\"user_id\"\": \"\"test_user_categories\"\"\n}')", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__chrome-mcp__chrome_screenshot", "mcp__chrome-mcp__chrome_console", "Bash(npm run dev:*)", "mcp__chrome-mcp__get_windows_and_tabs", "mcp__chrome-mcp__chrome_network_debugger_start", "mcp__chrome-mcp__chrome_network_debugger_stop", "mcp__chrome-mcp__chrome_network_request", "Bash(npm run build:*)", "mcp__chrome-mcp__chrome_inject_script", "mcp__chrome-mcp__chrome_get_interactive_elements", "mcp__chrome-mcp__chrome_network_capture_start", "mcp__chrome-mcp__chrome_network_capture_stop", "mcp__chrome-mcp__chrome_send_command_to_inject_script"], "deny": []}}