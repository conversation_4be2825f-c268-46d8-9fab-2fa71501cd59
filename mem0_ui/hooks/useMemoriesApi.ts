import { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Memory, Category } from '@/components/types';
import { AppDispatch, RootState } from '@/store/store';
import { setAccessLogs, setMemoriesSuccess, setSelectedMemory, setRelatedMemories } from '@/store/memoriesSlice';
import { mem0Client } from '@/lib/mem0-client';
import { realMem0Client } from '@/lib/mem0-client/realClient';
import { Mem0Memory, Mem0MemorySearchRequest, Mem0ApiError } from '@/types/mem0-api';
import { useErrorHandler } from '@/hooks/useErrorHandler';

// 简化的记忆类型，与现有组件兼容
export interface SimpleMemory {
  id: string;
  text: string;
  created_at: string;
  state: string;
  categories: string[];
  app_name: string;
}

// Mem0Memory到Memory的转换函数
const convertMem0MemoryToMemory = (mem0Memory: Mem0Memory): Memory => ({
  id: mem0Memory.id,
  memory: mem0Memory.text || mem0Memory.memory,
  created_at: mem0Memory.created_at ? new Date(mem0Memory.created_at).getTime() : Date.now(),
  state: (mem0Memory.state || 'active') as "active" | "paused" | "archived" | "deleted",
  metadata: mem0Memory.metadata || {},
  // 🔧 修复：从metadata.categories中提取categories，如果不存在则尝试根级别的categories
  categories: (mem0Memory.metadata?.categories || mem0Memory.categories || []) as Category[],
  client: 'api',
  app_name: mem0Memory.app_name || 'unknown'
});

// Memory到SimpleMemory的转换函数
const convertMemoryToSimpleMemory = (memory: Memory): SimpleMemory => ({
  id: memory.id,
  text: memory.memory,
  created_at: new Date(memory.created_at).toISOString(),
  state: memory.state,
  categories: memory.categories,
  app_name: memory.app_name
});

// 保留访问日志相关类型定义以兼容现有组件

interface AccessLogEntry {
  id: string;
  app_name: string;
  accessed_at: string;
}

interface AccessLogResponse {
  total: number;
  page: number;
  page_size: number;
  logs: AccessLogEntry[];
}

interface RelatedMemoryItem {
  id: string;
  content: string;
  created_at: number;
  state: string;
  app_id: string;
  app_name: string;
  categories: string[];
  metadata_: Record<string, any>;
}

interface RelatedMemoriesResponse {
  items: RelatedMemoryItem[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

interface UseMemoriesApiReturn {
  fetchMemories: (
    query?: string,
    page?: number,
    size?: number,
    filters?: {
      apps?: string[];
      categories?: string[];
      sortColumn?: string;
      sortDirection?: 'asc' | 'desc';
      showArchived?: boolean;
    }
  ) => Promise<{ memories: Memory[]; total: number; pages: number }>;
  fetchMemoryById: (memoryId: string) => Promise<void>;
  fetchAccessLogs: (memoryId: string, page?: number, pageSize?: number) => Promise<void>;
  fetchRelatedMemories: (memoryId: string) => Promise<void>;
  createMemory: (text: string) => Promise<void>;
  deleteMemories: (memoryIds: string[]) => Promise<void>;
  updateMemory: (memoryId: string, content: string) => Promise<void>;
  updateMemoryState: (memoryIds: string[], state: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  hasUpdates: number;
  memories: Memory[];
  selectedMemory: SimpleMemory | null;
}

export const useMemoriesApi = (): UseMemoriesApiReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasUpdates, setHasUpdates] = useState<number>(0);
  const dispatch = useDispatch<AppDispatch>();
  const { userId: user_id, viewMode } = useSelector((state: RootState) => state.profile);
  const memories = useSelector((state: RootState) => state.memories.memories);
  const selectedMemory = useSelector((state: RootState) => state.memories.selectedMemory);

  // 使用统一的错误处理系统
  const { error, handleApiError, clearError } = useErrorHandler();

  const fetchMemories = useCallback(async (
    query?: string,
    page: number = 1,
    size: number = 10,
    filters?: {
      apps?: string[];
      categories?: string[];
      sortColumn?: string;
      sortDirection?: 'asc' | 'desc';
      showArchived?: boolean;
    }
  ): Promise<{ memories: Memory[], total: number, pages: number }> => {
    setIsLoading(true);
    setError(null);
    try {
      let response: any;

      if (viewMode === 'system') {
        // 系统级视图：获取所有用户的记忆
        if (query && query.trim() !== '') {
          // 使用系统级搜索API
          try {
            response = await realMem0Client.searchSystemMemories({
              query: query.trim(),
              limit: size,
              offset: (page - 1) * size,
              filters: {
                ...(filters?.apps && { app_name: filters.apps }),
                ...(filters?.categories && { categories: filters.categories }),
                ...(!filters?.showArchived && { state: ['active', 'paused'] })
              }
            });
          } catch (systemSearchError) {
            console.warn('System search API not available, falling back to user search');
            // 如果系统级搜索不可用，回退到用户搜索
            const searchRequest: Mem0MemorySearchRequest = {
              user_id: user_id,
              query: query.trim(),
              limit: size,
              filters: {
                ...(filters?.apps && { app_name: filters.apps }),
                ...(filters?.categories && { categories: filters.categories }),
                ...(!filters?.showArchived && { state: ['active', 'paused'] })
              }
            };
            response = await mem0Client.searchMemories(searchRequest);
          }
        } else {
          // 使用系统级获取记忆API
          try {
            console.log('🚀 [useMemoriesApi] Calling getAllSystemMemories with params:', {
              limit: size,
              offset: (page - 1) * size,
              filters: {
                ...(filters?.apps && { app_name: filters.apps }),
                ...(filters?.categories && { categories: filters.categories }),  
                ...(!filters?.showArchived && { state: ['active', 'paused'] })
              }
            });
            
            response = await realMem0Client.getAllSystemMemories({
              limit: size,
              offset: (page - 1) * size,
              filters: {
                ...(filters?.apps && { app_name: filters.apps }),
                ...(filters?.categories && { categories: filters.categories }),  
                ...(!filters?.showArchived && { state: ['active', 'paused'] })
              }
            });
            
            console.log('✅ [useMemoriesApi] getAllSystemMemories response:', {
              memoriesCount: response.memories?.length || 0,
              total: response.total,
              usersCount: response.users?.length || 0,
              firstMemory: response.memories?.[0] ? {
                id: response.memories[0].id,
                text: response.memories[0].memory || response.memories[0].text,
                user_id: response.memories[0].user_id
              } : null
            });
          } catch (systemGetError) {
            console.warn('System get API not available, falling back to user get');
            // 如果系统级获取不可用，回退到用户获取
            const getRequest: any = {
              user_id: user_id,
              limit: size,
              offset: (page - 1) * size,
              filters: {
                ...(filters?.apps && { app_name: filters.apps }),
                ...(filters?.categories && { categories: filters.categories }),
                ...(!filters?.showArchived && { state: ['active', 'paused'] })
              }
            };
            response = await mem0Client.getMemories(getRequest);
          }
        }
      } else {
        // 单用户视图：仅获取当前用户的记忆
        if (query && query.trim() !== '') {
          const searchRequest: Mem0MemorySearchRequest = {
            user_id: user_id,
            query: query.trim(),
            limit: size,
            filters: {
              ...(filters?.apps && { app_name: filters.apps }),
              ...(filters?.categories && { categories: filters.categories }),
              ...(!filters?.showArchived && { state: ['active', 'paused'] })
            }
          };
          response = await mem0Client.searchMemories(searchRequest);
        } else {
          const getRequest: any = {
            user_id: user_id,
            limit: size,
            offset: (page - 1) * size,
            filters: {
              ...(filters?.apps && { app_name: filters.apps }),
              ...(filters?.categories && { categories: filters.categories }),
              ...(!filters?.showArchived && { state: ['active', 'paused'] })
            }
          };
          response = await mem0Client.getMemories(getRequest);
        }
      }

      // 🔧 修复：安全访问响应数据
      const memories = response?.memories || (Array.isArray(response) ? response : []);
      console.log('🔍 [useMemoriesApi] Response data adaptation:', {
        hasMemoriesProperty: response?.memories !== undefined,
        isResponseArray: Array.isArray(response),
        memoriesCount: memories.length,
        viewMode,
        sampleMemory: memories[0] ? {
          id: memories[0].id,
          memory: memories[0].memory || memories[0].text,
          user_id: memories[0].user_id
        } : null
      });

      // 转换Mem0Memory到Memory格式
      const adaptedMemories: Memory[] = memories.map(convertMem0MemoryToMemory);

      setIsLoading(false);
      dispatch(setMemoriesSuccess(adaptedMemories));

      return {
        memories: adaptedMemories,
        total: response.total,
        pages: Math.ceil(response.total / size)
      };
    } catch (err: any) {
      setIsLoading(false);
      handleApiError(err, 'fetch memories');
      throw err;
    }
  }, [user_id, viewMode, dispatch]);

  const createMemory = async (text: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await mem0Client.createMemory({
        messages: [{ role: 'user', content: text }],
        user_id: user_id,
        app_name: "mem0-ui"
      });
      setIsLoading(false);
      setHasUpdates(hasUpdates + 1);
    } catch (err: any) {
      setIsLoading(false);
      handleApiError(err, 'create memory');
      throw err;
    }
  };

  const deleteMemories = async (memory_ids: string[]): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      // 使用批量操作删除记忆
      await mem0Client.batchOperation({
        memory_ids: memory_ids
      });

      // 更新本地状态
      dispatch(setMemoriesSuccess(memories.filter((memory: Memory) => !memory_ids.includes(memory.id))));
      setIsLoading(false);
      setHasUpdates(hasUpdates + 1);
    } catch (err: any) {
      setIsLoading(false);
      handleApiError(err, 'delete memories');
      throw err;
    }
  };

  const fetchMemoryById = async (memoryId: string): Promise<void> => {
    if (memoryId === "") {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const mem0Memory = await mem0Client.getMemory(memoryId, user_id);
      const simpleMemory: SimpleMemory = {
        id: mem0Memory.id,
        text: mem0Memory.text || mem0Memory.memory,
        created_at: mem0Memory.created_at || new Date().toISOString(),
        state: mem0Memory.state || 'active',
        categories: mem0Memory.categories || [],
        app_name: mem0Memory.app_name || 'unknown'
      };

      setIsLoading(false);
      dispatch(setSelectedMemory(simpleMemory));
    } catch (err: any) {
      setIsLoading(false);
      handleApiError(err, 'fetch memory by id');
      throw err;
    }
  };

  const fetchAccessLogs = async (memoryId: string, page: number = 1, pageSize: number = 10): Promise<void> => {
    if (memoryId === "") {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      // 注意：Mem0核心API可能不包含访问日志功能
      // 这里提供一个占位符实现，返回空数组
      // 如果需要此功能，可以通过活动日志API获取相关信息
      // const activities = await mem0Client.getActivities(user_id, pageSize, (page - 1) * pageSize);
      // const memoryActivities = activities.activities.filter(activity => activity.memory_id === memoryId);

      // 转换为访问日志格式（兼容现有组件）
      const accessLogs: AccessLogEntry[] = []; // 暂时返回空数组

      setIsLoading(false);
      dispatch(setAccessLogs(accessLogs));
    } catch (err: any) {
      setIsLoading(false);
      handleApiError(err, 'fetch access logs');
      throw err;
    }
  };

  const fetchRelatedMemories = async (memoryId: string): Promise<void> => {
    if (memoryId === "") {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      // 注意：Mem0核心API可能不包含相关记忆功能
      // 这里提供一个简化实现，通过搜索相似内容来模拟相关记忆
      const currentMemory = await mem0Client.getMemory(memoryId, user_id);

      // 使用记忆文本的前50个字符作为搜索查询
      const searchQuery = (currentMemory.text || currentMemory.memory).substring(0, 50);
      const searchResult = await mem0Client.searchMemories({
        user_id: user_id,
        query: searchQuery,
        limit: 5
      });

      // 过滤掉当前记忆本身
      const relatedMem0Memories = searchResult.memories.filter((memory: any) => memory.id !== memoryId);
      const adaptedMemories: Memory[] = relatedMem0Memories.map(convertMem0MemoryToMemory);

      setIsLoading(false);
      dispatch(setRelatedMemories(adaptedMemories));
    } catch (err: any) {
      setIsLoading(false);
      handleApiError(err, 'fetch related memories');
      throw err;
    }
  };

  const updateMemory = async (memoryId: string, content: string): Promise<void> => {
    if (memoryId === "") {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      await mem0Client.updateMemory(memoryId, {
        text: content
      });
      setIsLoading(false);
      setHasUpdates(hasUpdates + 1);
    } catch (err: any) {
      setIsLoading(false);
      handleApiError(err, 'update memory');
      throw err;
    }
  };

  const updateMemoryState = async (memoryIds: string[], state: string): Promise<void> => {
    if (memoryIds.length === 0) {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      // 使用批量操作更新记忆状态
      await mem0Client.batchOperation({
        memory_ids: memoryIds,
        data: {
          state: state as 'active' | 'paused' | 'archived' | 'deleted'
        }
      });

      // 更新本地状态
      dispatch(setMemoriesSuccess(memories.map((memory: Memory) => {
        if (memoryIds.includes(memory.id)) {
          return { ...memory, state: state as "active" | "paused" | "archived" | "deleted" };
        }
        return memory;
      })));

      // 如果是归档，从列表中移除
      if (state === "archived") {
        dispatch(setMemoriesSuccess(memories.filter((memory: Memory) => !memoryIds.includes(memory.id))));
      }

      // 更新选中的记忆状态
      if (selectedMemory?.id && memoryIds.includes(selectedMemory.id)) {
        dispatch(setSelectedMemory({ ...selectedMemory, state: state as "active" | "paused" | "archived" | "deleted" }));
      }

      setIsLoading(false);
      setHasUpdates(hasUpdates + 1);
    } catch (err: any) {
      setIsLoading(false);
      handleApiError(err, 'update memory state');
      throw err;
    }
  };

  return {
    fetchMemories,
    fetchMemoryById,
    fetchAccessLogs,
    fetchRelatedMemories,
    createMemory,
    deleteMemories,
    updateMemory,
    updateMemoryState,
    isLoading,
    error,
    hasUpdates,
    memories,
    selectedMemory
  };
};