/**
 * 性能监控和优化Hook
 * 
 * 提供组件性能监控和优化功能：
 * - 渲染时间监控
 * - 内存使用监控
 * - 性能优化建议
 * - 自动性能调优
 */

import { useEffect, useRef, useCallback, useState } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  componentMounts: number;
  componentUpdates: number;
  lastRenderTime: number;
}

interface PerformanceConfig {
  enableMonitoring: boolean;
  sampleRate: number;
  maxSamples: number;
  warningThresholds: {
    renderTime: number;
    memoryUsage: number;
  };
}

const defaultConfig: PerformanceConfig = {
  enableMonitoring: process.env.NODE_ENV === 'development',
  sampleRate: 1.0,
  maxSamples: 100,
  warningThresholds: {
    renderTime: 16, // 60fps = 16.67ms per frame
    memoryUsage: 50 * 1024 * 1024 // 50MB
  }
};

/**
 * 性能监控Hook
 */
export function usePerformanceMonitor(
  componentName: string,
  config: Partial<PerformanceConfig> = {}
) {
  const finalConfig = { ...defaultConfig, ...config };
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    componentMounts: 0,
    componentUpdates: 0,
    lastRenderTime: 0
  });

  const renderStartTime = useRef<number>(0);
  const samplesRef = useRef<number[]>([]);
  const mountCountRef = useRef(0);
  const updateCountRef = useRef(0);

  // 开始渲染计时
  const startRender = useCallback(() => {
    if (!finalConfig.enableMonitoring || Math.random() > finalConfig.sampleRate) {
      return;
    }
    renderStartTime.current = performance.now();
  }, [finalConfig.enableMonitoring, finalConfig.sampleRate]);

  // 结束渲染计时
  const endRender = useCallback(() => {
    if (!finalConfig.enableMonitoring || renderStartTime.current === 0) {
      return;
    }

    const renderTime = performance.now() - renderStartTime.current;
    renderStartTime.current = 0;

    // 记录样本
    samplesRef.current.push(renderTime);
    if (samplesRef.current.length > finalConfig.maxSamples) {
      samplesRef.current.shift();
    }

    // 获取内存使用情况
    const memoryUsage = (performance as any).memory?.usedJSHeapSize || 0;

    // 更新指标
    setMetrics(prev => ({
      ...prev,
      renderTime,
      memoryUsage,
      lastRenderTime: Date.now()
    }));

    // 性能警告
    if (renderTime > finalConfig.warningThresholds.renderTime) {
      console.warn(
        `[Performance] ${componentName} render time: ${renderTime.toFixed(2)}ms (threshold: ${finalConfig.warningThresholds.renderTime}ms)`
      );
    }

    if (memoryUsage > finalConfig.warningThresholds.memoryUsage) {
      console.warn(
        `[Performance] ${componentName} memory usage: ${(memoryUsage / 1024 / 1024).toFixed(2)}MB`
      );
    }
  }, [componentName, finalConfig]);

  // 组件挂载计数
  useEffect(() => {
    mountCountRef.current++;
    setMetrics(prev => ({
      ...prev,
      componentMounts: mountCountRef.current
    }));
  }, []);

  // 组件更新计数
  useEffect(() => {
    updateCountRef.current++;
    setMetrics(prev => ({
      ...prev,
      componentUpdates: updateCountRef.current
    }));
  });

  // 获取性能统计
  const getStats = useCallback(() => {
    const samples = samplesRef.current;
    if (samples.length === 0) {
      return null;
    }

    const sum = samples.reduce((a, b) => a + b, 0);
    const avg = sum / samples.length;
    const sorted = [...samples].sort((a, b) => a - b);
    const p95 = sorted[Math.floor(samples.length * 0.95)];
    const p99 = sorted[Math.floor(samples.length * 0.99)];

    return {
      average: avg,
      p95,
      p99,
      min: Math.min(...samples),
      max: Math.max(...samples),
      samples: samples.length
    };
  }, []);

  return {
    metrics,
    startRender,
    endRender,
    getStats
  };
}

/**
 * 渲染性能Hook
 */
export function useRenderPerformance(componentName: string) {
  const { startRender, endRender, metrics, getStats } = usePerformanceMonitor(componentName);

  // 自动在渲染前后调用
  useEffect(() => {
    startRender();
    return () => {
      endRender();
    };
  });

  return { metrics, getStats };
}

/**
 * 内存监控Hook
 */
export function useMemoryMonitor(interval: number = 5000) {
  const [memoryInfo, setMemoryInfo] = useState<{
    used: number;
    total: number;
    limit: number;
  } | null>(null);

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ((performance as any).memory) {
        const memory = (performance as any).memory;
        setMemoryInfo({
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        });
      }
    };

    updateMemoryInfo();
    const timer = setInterval(updateMemoryInfo, interval);

    return () => clearInterval(timer);
  }, [interval]);

  return memoryInfo;
}

/**
 * 性能优化建议Hook
 */
export function usePerformanceOptimization(metrics: PerformanceMetrics) {
  const suggestions = useCallback(() => {
    const suggestions: string[] = [];

    if (metrics.renderTime > 16) {
      suggestions.push('Consider using React.memo() to prevent unnecessary re-renders');
      suggestions.push('Use useMemo() and useCallback() for expensive calculations');
    }

    if (metrics.renderTime > 50) {
      suggestions.push('Consider code splitting with React.lazy()');
      suggestions.push('Implement virtualization for large lists');
    }

    if (metrics.componentUpdates > metrics.componentMounts * 10) {
      suggestions.push('Too many re-renders detected - check dependencies in useEffect');
    }

    if (metrics.memoryUsage > 100 * 1024 * 1024) {
      suggestions.push('High memory usage - check for memory leaks');
      suggestions.push('Consider implementing data pagination');
    }

    return suggestions;
  }, [metrics]);

  return suggestions();
}

/**
 * 自动性能调优Hook
 */
export function useAutoPerformanceTuning(componentName: string) {
  const [optimizationLevel, setOptimizationLevel] = useState<'low' | 'medium' | 'high'>('medium');
  const { metrics } = usePerformanceMonitor(componentName);

  useEffect(() => {
    // 根据性能指标自动调整优化级别
    if (metrics.renderTime > 50 || metrics.memoryUsage > 100 * 1024 * 1024) {
      setOptimizationLevel('high');
    } else if (metrics.renderTime > 16 || metrics.memoryUsage > 50 * 1024 * 1024) {
      setOptimizationLevel('medium');
    } else {
      setOptimizationLevel('low');
    }
  }, [metrics]);

  const getOptimizationConfig = useCallback(() => {
    switch (optimizationLevel) {
      case 'high':
        return {
          enableVirtualization: true,
          maxItemsPerPage: 20,
          enableLazyLoading: true,
          debounceDelay: 500
        };
      case 'medium':
        return {
          enableVirtualization: false,
          maxItemsPerPage: 50,
          enableLazyLoading: true,
          debounceDelay: 300
        };
      case 'low':
      default:
        return {
          enableVirtualization: false,
          maxItemsPerPage: 100,
          enableLazyLoading: false,
          debounceDelay: 100
        };
    }
  }, [optimizationLevel]);

  return {
    optimizationLevel,
    config: getOptimizationConfig(),
    metrics
  };
}

/**
 * 性能分析器Hook
 */
export function usePerformanceProfiler(enabled: boolean = false) {
  const profileRef = useRef<{
    startTime: number;
    marks: Array<{ name: string; time: number }>;
  }>({ startTime: 0, marks: [] });

  const startProfiling = useCallback((name: string = 'profile') => {
    if (!enabled) return;
    
    profileRef.current.startTime = performance.now();
    profileRef.current.marks = [];
    performance.mark(`${name}-start`);
  }, [enabled]);

  const mark = useCallback((name: string) => {
    if (!enabled) return;
    
    const time = performance.now();
    profileRef.current.marks.push({ name, time });
    performance.mark(name);
  }, [enabled]);

  const endProfiling = useCallback((name: string = 'profile') => {
    if (!enabled) return;
    
    const endTime = performance.now();
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const totalTime = endTime - profileRef.current.startTime;
    const marks = profileRef.current.marks.map(mark => ({
      ...mark,
      relativeTime: mark.time - profileRef.current.startTime
    }));

    return {
      totalTime,
      marks,
      startTime: profileRef.current.startTime,
      endTime
    };
  }, [enabled]);

  return {
    startProfiling,
    mark,
    endProfiling
  };
}
