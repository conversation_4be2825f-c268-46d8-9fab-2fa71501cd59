/**
 * 移动端图界面组件
 * 
 * 专为移动设备优化的Graph Memory界面：
 * - 触摸友好的交互设计
 * - 响应式布局适配
 * - 手势支持（缩放、平移、选择）
 * - 简化的UI元素
 * - 性能优化的渲染
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  Menu, 
  Search, 
  Filter, 
  ZoomIn, 
  ZoomOut, 
  Maximize2, 
  Settings,
  ChevronUp,
  ChevronDown,
  X
} from 'lucide-react';

import { RootState } from '@/store/store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Sheet, SheetContent, SheetTrigger, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { useIsMobile } from '@/components/ui/use-mobile';
import { updateFilters, updateViewState } from '@/store/graphMemorySlice';

import GraphVisualization from './GraphVisualization';

interface MobileGraphInterfaceProps {
  className?: string;
}

interface TouchGesture {
  type: 'pan' | 'pinch' | 'tap' | 'long-press';
  startTime: number;
  startPosition: { x: number; y: number };
  currentPosition: { x: number; y: number };
  scale?: number;
  distance?: number;
}

const MobileGraphInterface: React.FC<MobileGraphInterfaceProps> = ({
  className = ''
}) => {
  const dispatch = useDispatch();
  const isMobile = useIsMobile();
  const { nodes, edges, filters, viewState, selectedNodeIds, selectedEdgeIds } = useSelector(
    (state: RootState) => state.graphMemory
  );

  // 移动端状态
  const [showFilters, setShowFilters] = useState(false);
  const [showControls, setShowControls] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentGesture, setCurrentGesture] = useState<TouchGesture | null>(null);

  // 触摸事件引用
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const gestureRef = useRef<TouchGesture | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 处理搜索
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    dispatch(updateFilters({ entity_search: query || undefined }));
  }, [dispatch]);

  // 处理缩放
  const handleZoomIn = useCallback(() => {
    const newZoom = Math.min(4, viewState.zoom * 1.2);
    dispatch(updateViewState({ zoom: newZoom }));
  }, [dispatch, viewState.zoom]);

  const handleZoomOut = useCallback(() => {
    const newZoom = Math.max(0.1, viewState.zoom * 0.8);
    dispatch(updateViewState({ zoom: newZoom }));
  }, [dispatch, viewState.zoom]);

  // 切换全屏
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // 处理触摸开始
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.touches[0];
    const startTime = Date.now();
    const startPosition = { x: touch.clientX, y: touch.clientY };

    touchStartRef.current = { x: touch.clientX, y: touch.clientY, time: startTime };

    if (e.touches.length === 1) {
      // 单指触摸
      const gesture: TouchGesture = {
        type: 'tap',
        startTime,
        startPosition,
        currentPosition: startPosition
      };
      setCurrentGesture(gesture);
      gestureRef.current = gesture;
    } else if (e.touches.length === 2) {
      // 双指触摸（缩放）
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) + 
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );

      const gesture: TouchGesture = {
        type: 'pinch',
        startTime,
        startPosition: {
          x: (touch1.clientX + touch2.clientX) / 2,
          y: (touch1.clientY + touch2.clientY) / 2
        },
        currentPosition: {
          x: (touch1.clientX + touch2.clientX) / 2,
          y: (touch1.clientY + touch2.clientY) / 2
        },
        distance,
        scale: 1
      };
      setCurrentGesture(gesture);
      gestureRef.current = gesture;
    }
  }, []);

  // 处理触摸移动
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    e.preventDefault(); // 防止页面滚动

    if (!gestureRef.current) return;

    const currentTime = Date.now();
    const gesture = gestureRef.current;

    if (e.touches.length === 1 && gesture.type === 'tap') {
      const touch = e.touches[0];
      const currentPosition = { x: touch.clientX, y: touch.clientY };
      const distance = Math.sqrt(
        Math.pow(currentPosition.x - gesture.startPosition.x, 2) + 
        Math.pow(currentPosition.y - gesture.startPosition.y, 2)
      );

      // 如果移动距离超过阈值，转换为平移手势
      if (distance > 10) {
        gesture.type = 'pan';
        gesture.currentPosition = currentPosition;
        setCurrentGesture({ ...gesture });
      }

      // 检查长按
      if (currentTime - gesture.startTime > 500 && distance < 10) {
        gesture.type = 'long-press';
        setCurrentGesture({ ...gesture });
      }
    } else if (e.touches.length === 2 && gesture.type === 'pinch') {
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) + 
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );

      if (gesture.distance) {
        const scale = currentDistance / gesture.distance;
        gesture.scale = scale;
        gesture.currentPosition = {
          x: (touch1.clientX + touch2.clientX) / 2,
          y: (touch1.clientY + touch2.clientY) / 2
        };
        setCurrentGesture({ ...gesture });

        // 更新视图缩放
        const newZoom = Math.max(0.1, Math.min(4, viewState.zoom * scale));
        dispatch(updateViewState({ zoom: newZoom }));
      }
    }
  }, [dispatch, viewState.zoom]);

  // 处理触摸结束
  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!gestureRef.current) return;

    const gesture = gestureRef.current;
    const endTime = Date.now();
    const duration = endTime - gesture.startTime;

    // 处理不同类型的手势
    switch (gesture.type) {
      case 'tap':
        if (duration < 300) {
          // 快速点击
          console.log('Quick tap detected');
        }
        break;

      case 'long-press':
        // 长按显示上下文菜单
        console.log('Long press detected');
        setShowControls(true);
        break;

      case 'pan':
        // 平移结束
        console.log('Pan gesture ended');
        break;

      case 'pinch':
        // 缩放结束
        console.log('Pinch gesture ended');
        break;
    }

    // 清理手势状态
    setCurrentGesture(null);
    gestureRef.current = null;
    touchStartRef.current = null;
  }, []);

  // 如果不是移动设备，返回桌面版本
  if (!isMobile) {
    return (
      <div className={className}>
        <GraphVisualization height="600px" />
      </div>
    );
  }

  // 获取活跃筛选器数量
  const activeFiltersCount = Object.values(filters).filter(Boolean).length;
  const hasSelection = selectedNodeIds.length > 0 || selectedEdgeIds.length > 0;

  return (
    <div 
      ref={containerRef}
      className={`mobile-graph-interface ${className} ${isFullscreen ? 'fixed inset-0 z-50 bg-zinc-950' : 'relative'}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-3 bg-zinc-900 border-b border-zinc-800">
        {/* 左侧：菜单和搜索 */}
        <div className="flex items-center space-x-2 flex-1">
          <Sheet open={showFilters} onOpenChange={setShowFilters}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="text-zinc-300">
                <Menu className="h-4 w-4" />
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-1 h-4 px-1 text-xs">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80 bg-zinc-900 border-zinc-800">
              <SheetHeader>
                <SheetTitle className="text-white">Filters & Settings</SheetTitle>
              </SheetHeader>
              <div className="mt-6 space-y-4">
                {/* 搜索框 */}
                <div>
                  <label className="text-sm font-medium text-zinc-300 mb-2 block">
                    Search
                  </label>
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-zinc-500" />
                    <Input
                      placeholder="Search entities..."
                      className="pl-8 bg-zinc-800 border-zinc-700"
                      value={searchQuery}
                      onChange={(e) => handleSearch(e.target.value)}
                    />
                  </div>
                </div>

                {/* 布局选择 */}
                <div>
                  <label className="text-sm font-medium text-zinc-300 mb-2 block">
                    Layout
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    {['force', 'hierarchical', 'circular', 'grid'].map((layout) => (
                      <Button
                        key={layout}
                        variant={viewState.layout === layout ? "default" : "outline"}
                        size="sm"
                        className="capitalize"
                        onClick={() => dispatch(updateViewState({ layout: layout as any }))}
                      >
                        {layout}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* 显示选项 */}
                <div>
                  <label className="text-sm font-medium text-zinc-300 mb-2 block">
                    Display Options
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={viewState.show_labels}
                        onChange={(e) => dispatch(updateViewState({ show_labels: e.target.checked }))}
                        className="rounded border-zinc-600"
                      />
                      <span className="text-sm text-zinc-300">Show Labels</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={viewState.show_edge_labels}
                        onChange={(e) => dispatch(updateViewState({ show_edge_labels: e.target.checked }))}
                        className="rounded border-zinc-600"
                      />
                      <span className="text-sm text-zinc-300">Show Edge Labels</span>
                    </label>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          {/* 搜索按钮 */}
          <Button
            variant="ghost"
            size="sm"
            className="text-zinc-300"
            onClick={() => setShowFilters(true)}
          >
            <Search className="h-4 w-4" />
          </Button>
        </div>

        {/* 右侧：控制按钮 */}
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            className="text-zinc-300"
            onClick={handleZoomOut}
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-zinc-300"
            onClick={handleZoomIn}
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-zinc-300"
            onClick={toggleFullscreen}
          >
            {isFullscreen ? <X className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* 图可视化区域 */}
      <div className="flex-1 relative">
        <GraphVisualization
          height={isFullscreen ? 'calc(100vh - 60px)' : '400px'}
          className="touch-manipulation"
        />

        {/* 选择信息 */}
        {hasSelection && (
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-zinc-900/95 backdrop-blur-sm border border-zinc-800 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div className="text-sm text-zinc-300">
                  Selected: {selectedNodeIds.length} nodes, {selectedEdgeIds.length} edges
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-zinc-400"
                  onClick={() => {
                    // 清除选择
                    // dispatch(clearSelection());
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 手势提示 */}
        {currentGesture && (
          <div className="absolute top-4 left-4 bg-zinc-900/95 backdrop-blur-sm border border-zinc-800 rounded-lg px-3 py-2">
            <div className="text-xs text-zinc-400">
              {currentGesture.type === 'pan' && 'Panning...'}
              {currentGesture.type === 'pinch' && `Zooming... ${(currentGesture.scale || 1).toFixed(2)}x`}
              {currentGesture.type === 'long-press' && 'Long press detected'}
            </div>
          </div>
        )}
      </div>

      {/* 底部状态栏 */}
      <div className="flex items-center justify-between p-2 bg-zinc-900 border-t border-zinc-800 text-xs text-zinc-400">
        <div>
          {nodes.length} nodes, {edges.length} edges
        </div>
        <div>
          Zoom: {(viewState.zoom * 100).toFixed(0)}%
        </div>
      </div>
    </div>
  );
};

export default MobileGraphInterface;
